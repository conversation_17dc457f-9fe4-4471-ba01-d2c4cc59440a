'use client';

import { useTranslations } from 'next-intl';

const AboutSection = () => {
  const t = useTranslations('hero');
  const features = useTranslations('features');

  return (
    <section className="min-h-screen flex">
      {/* Left Half - Image */}
      <div className="w-1/2 relative overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/building.png)'
          }}
        ></div>
        {/* Optional overlay for better text contrast if needed */}
        <div className="absolute inset-0 bg-black/10"></div>
      </div>

      {/* Right Half - Content */}
      <div className="w-1/2 bg-albatros-ivory relative flex items-center justify-center p-12">
        <div className="grain"></div>
        <div className="max-w-2xl h-full max-h-[600px] flex flex-col justify-between relative">
          {/* Main Title */}
          <div className="flex flex-col">
            <h2 className="text-4xl lg:text-5xl mb-4 font-bold text-albatros-black leading-tight">
                Uz Albatros dokumenti klijenata stižu brzo i sigurno, bez čekanja i nepotrebnih briga.
            </h2>
          </div>

          {/* Features List */}
          <div className="flex flex-col text-xl text-black/80 border-t border-t-black/80">
            <p className="py-3 border-b border-b-black/80">{features('fast')}</p>
            <p className="py-3 border-b border-b-black/80">{features('reliable')}</p>
            <p className="py-3 border-b border-b-black/80">{features('discrete')}</p>
          </div>

          {/* Bottom Text */}
          <div className="pt-8 border-t border-albatros-black/10">
            <p className="text-sm text-black leading-tight">
              {t('cta')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
