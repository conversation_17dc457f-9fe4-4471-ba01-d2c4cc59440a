'use client';

import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import gradientGL from 'gradient-gl';

const FeaturesSection = () => {
  useEffect(() => {
    // Initialize gradient-gl for features section
    gradientGL('b5.fc04', '#features-gradient-bg');
  }, []);

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: glassRef, inView: glassInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: descriptionRef, inView: descriptionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: servicesRef, inView: servicesInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: noteRef, inView: noteInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: benefitsRef, inView: benefitsInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div
        id="features-gradient-bg"
        className={`gradient-container absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-1500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>
      {/* <div className="grain !opacity-20"></div> */}


      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        <div className="flex items-center">
          {/* Left side - Glass morphism container */}
          <div className="w-full md:w-1/2 lg:w-1/2">
            <div
              ref={glassRef}
              className={`bg-white/10 backdrop-blur-[15px] rounded-3xl p-12 transition-all duration-1000 ease-out ${
                glassInView
                  ? 'opacity-100 translate-y-0 scale-100'
                  : 'opacity-0 translate-y-8 scale-95'
              }`}
            >
              {/* Main Title */}
              <h2
                ref={titleRef}
                className={`text-4xl lg:text-5xl font-bold text-albatros-ivory mb-8 leading-tight transition-all duration-1000 ease-out delay-800 ${
                  titleInView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                Naše usluge
              </h2>

              {/* Services List */}
              <div
                ref={servicesRef}
                className={`flex flex-col text-lg text-albatros-ivory mb-8 transition-all duration-1000 ease-out delay-1200 ${
                  servicesInView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Pribavljanje i dostava ličnih dokumenata</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Pribavljanje i dostava izvoda iz matičnih knjiga</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Pribavljanje i dostava potvrda i uvjerenja</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Ovjera kod nadležnih institucija (općina, notar, sud)</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Pribavljanje i dostava zemljišnoknjižnih izvadaka</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Administrativna podrška pravnim licima</span>
                  <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-3">Organizacija prijevoda kod sudskog tumača</span>
              </div>

              <p
                ref={noteRef}
                className={`text-sm text-albatros-ivory/80 mb-8 font-bold italic transition-all duration-1000 ease-out delay-1400 ${
                  noteInView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                Djelatnosti OD Albatros proširuju se shodno Vašim administrativnim potrebama.
              </p>

              {/* Important Notes */}
              <div
                ref={benefitsRef}
                className={`space-y-4 mt-8 pt-6 border-t border-white/20 transition-all duration-1000 ease-out delay-1600 ${
                  benefitsInView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <h3 className="text-lg text-albatros-ivory mb-4 leading-tight">Napomena:</h3>
                <div className="flex flex-col font-semibold text-sm text-albatros-ivory/90">
                    <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Vremenski rok pribavljanja dokumenata zavisi od vrste dokumenata i vremena potrebnog institucijama.</span>
                    <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Za obavljanje naših djelatnosti potrebna nam je Vaša ovjerena punomoć.</span>
                    <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Građani BiH u BiH punomoć vade i ovjeravaju u općini.</span>
                    <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Državljani Bosne i Hercegovine u inostranstvu punomoć mogu ovjeriti u konzulatu.</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
