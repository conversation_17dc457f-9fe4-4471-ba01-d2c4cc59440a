'use client';

import { useInView } from 'react-intersection-observer';

const WhatWeDoSection = () => {
  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: item1Ref, inView: item1InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: item2Ref, inView: item2InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: item3Ref, inView: item3InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: item4Ref, inView: item4InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: bottomRef, inView: bottomInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: cornerTextRef, inView: cornerTextInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  return (
    <section className="py-24 relative bg-albatros-ivory">
      <div className="grain"></div>
      <div className="max-w-7xl relative mx-auto px-6 lg:px-8">
        <div className="flex flex-col md:flex-row">
          {/* Left 1/3 - Small corner text */}
          <div className="mb-5 md:w-1/3 md:mb-0 pr-12">
            <div
              ref={cornerTextRef}
              className={`pt-8 transition-all duration-1000 ease-out delay-100 ${
                cornerTextInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <p className="text-sm text-black font-light">
                <span className="font-bold">Albatros</span><br />
                Administrativne usluge
              </p>
            </div>
          </div>

          {/* Right 2/3 - Main content */}
          <div className="md:w-2/3">
            {/* Main Title */}
            <div
              ref={titleRef}
              className={`mb-16 transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-7xl font-normal text-black">
                Vaš pouzdan partner u administrativnim poslovima
              </h2>
            </div>

            {/* 2x2 Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-12 mb-16">
              {/* Top Left */}
              <div
                ref={item1Ref}
                className={`space-y-4 transition-all duration-1000 ease-out delay-500 ${
                  item1InView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <div className="text-black text-xl font-medium mb-2">01</div>
                <h3 className="text-4xl font-medium text-black mb-2 tracking-tight">
                  Državljani BiH
                </h3>
                <p className="text-black text-xl tracking-tight">
                  Naše usluge su namijenjene državljanima BiH koji žive u BiH ili u inostranstvu.
                </p>
              </div>

              {/* Top Right */}
              <div
                ref={item2Ref}
                className={`space-y-4 transition-all duration-1000 ease-out delay-700 ${
                  item2InView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <div className="text-black text-xl font-medium mb-2">02</div>
                <h3 className="text-4xl font-medium text-black mb-2 tracking-tight">
                  Bivši stanovnici
                </h3>
                <p className="text-black text-xl tracking-tight">
                  Osobama koje su imale prebivalište u BiH omogućavamo brzu obradu dokumenata.
                </p>
              </div>

              {/* Bottom Left */}
              <div
                ref={item3Ref}
                className={`space-y-4 transition-all duration-1000 ease-out delay-900 ${
                  item3InView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <div className="text-black text-xl font-medium mb-2">03</div>
                <h3 className="text-4xl font-medium text-black mb-2 tracking-tight">
                  Firme i organizacije
                </h3>
                <p className="text-black text-xl tracking-tight">
                  Firmama kojima je potrebna administrativna podrška pružamo profesionalnu pomoć.
                </p>
              </div>

              {/* Bottom Right */}
              <div
                ref={item4Ref}
                className={`space-y-4 transition-all duration-1000 ease-out delay-1100 ${
                  item4InView
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                <div className="text-black text-xl font-medium mb-2">04</div>
                <h3 className="text-4xl font-medium text-black mb-2 tracking-tight">
                  Globalna dostupnost
                </h3>
                <p className="text-black text-xl tracking-tight">
                  Bilo da ste u ili van Bosne i Hercegovine, Albatros Vam omogućava brzu i sigurnu uslugu.
                </p>
              </div>
            </div>

            {/* Bottom text */}
            <div
              ref={bottomRef}
              className={`pt-8 border-t border-black/10 transition-all duration-1000 ease-out delay-900 ${
                bottomInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <p className="text-sm text-black leading-tight mb-6">
                Naš cilj je da Vam omogućimo rješavanje administrativnih procedura jednostavnije, brže i bez stresa.
                Iskustvom, profesionalnim pristupom i diskrecijom gradimo odnos povjerenja sa svakim klijentom.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatWeDoSection;
